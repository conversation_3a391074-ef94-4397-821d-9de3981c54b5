[32mINFO    [0m pytest_result_log:plugin.py:122 Start: testcase/test_login.py::TestLogin::test_login[su-wrong_password-failure-invalid_password]
[32mINFO    [0m selenium_test:logger.py:95 ====== 测试开始: test_login ======
[32mINFO    [0m selenium_test:logger.py:95 初始化浏览器: chrome, 无头模式: False
[32mINFO    [0m WDM:logger.py:11 ====== WebDriver manager ======
[32mINFO    [0m WDM:logger.py:11 Get LATEST chromedriver version for google-chrome
[32mINFO    [0m WDM:logger.py:11 Get LATEST chromedriver version for google-chrome
[32mINFO    [0m WDM:logger.py:11 Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\135.0.7049.97\chromedriver-win32/chromedriver.exe] found in cache
[32mINFO    [0m selenium_test:logger.py:95 测试登录功能, 用户名: su, 密码: wrong_password, 期望结果: failure
[32mINFO    [0m selenium_test:login_page.py:30 打开登录页面: http://127.0.0.1:8080/login/index.html
[32mINFO    [0m selenium_test:login_page.py:33 页面已最大化
[32mINFO    [0m selenium_test:login_page.py:46 开始登录操作, 用户名: su
[32mINFO    [0m selenium_test:base_page.py:76 在元素 ('id', 'usernamefield-inputEl') 中输入文本: su
[32mINFO    [0m selenium_test:base_page.py:39 查找元素: ('id', 'usernamefield-inputEl')
[32mINFO    [0m selenium_test:base_page.py:44 元素已找到: ('id', 'usernamefield-inputEl')
[32mINFO    [0m selenium_test:base_page.py:80 文本输入完成: ('id', 'usernamefield-inputEl')
[32mINFO    [0m selenium_test:base_page.py:76 在元素 ('id', 'passwordfield-inputEl') 中输入文本: wrong_password
[32mINFO    [0m selenium_test:base_page.py:39 查找元素: ('id', 'passwordfield-inputEl')
[32mINFO    [0m selenium_test:base_page.py:44 元素已找到: ('id', 'passwordfield-inputEl')
[32mINFO    [0m selenium_test:base_page.py:80 文本输入完成: ('id', 'passwordfield-inputEl')
[32mINFO    [0m selenium_test:base_page.py:60 点击元素: ('class name', 'loginButton')
[32mINFO    [0m selenium_test:base_page.py:39 查找元素: ('class name', 'loginButton')
[32mINFO    [0m selenium_test:base_page.py:44 元素已找到: ('class name', 'loginButton')
[32mINFO    [0m selenium_test:base_page.py:63 元素已点击: ('class name', 'loginButton')
[32mINFO    [0m selenium_test:login_page.py:50 登录操作完成
[32mINFO    [0m selenium_test:login_page.py:141 等待登录完成，超时时间: 10秒
[32mINFO    [0m selenium_test:base_page.py:108 检查元素是否存在: ('xpath', "//i[@title='退出']")
[32mINFO    [0m selenium_test:base_page.py:39 查找元素: ('xpath', "//i[@title='退出']")
[1m[31mERROR   [0m selenium_test:base_page.py:47 元素未找到: ('xpath', "//i[@title='退出']")
[32mINFO    [0m selenium_test:base_page.py:114 元素不存在: ('xpath', "//i[@title='退出']")
[32mINFO    [0m selenium_test:base_page.py:108 检查元素是否存在: ('xpath', "//div[contains(@class, 'error-message')]")
[32mINFO    [0m selenium_test:base_page.py:39 查找元素: ('xpath', "//div[contains(@class, 'error-message')]")
[1m[31mERROR   [0m selenium_test:base_page.py:47 元素未找到: ('xpath', "//div[contains(@class, 'error-message')]")
[32mINFO    [0m selenium_test:base_page.py:114 元素不存在: ('xpath', "//div[contains(@class, 'error-message')]")
[1m[31mERROR   [0m selenium_test:login_page.py:150 等待登录完成超时
[32mINFO    [0m selenium_test:logger.py:95 验证登录结果
[32mINFO    [0m selenium_test:logger.py:95 测试通过: 登录失败
[32mINFO    [0m selenium_test:login_page.py:94 获取登录错误信息
[32mINFO    [0m selenium_test:base_page.py:108 检查元素是否存在: ('xpath', "//div[contains(@class, 'error-message')]")
[32mINFO    [0m selenium_test:base_page.py:39 查找元素: ('xpath', "//div[contains(@class, 'error-message')]")
[1m[31mERROR   [0m selenium_test:base_page.py:47 元素未找到: ('xpath', "//div[contains(@class, 'error-message')]")
[32mINFO    [0m selenium_test:base_page.py:114 元素不存在: ('xpath', "//div[contains(@class, 'error-message')]")
[32mINFO    [0m pytest_result_log:plugin.py:128 End: testcase/test_login.py::TestLogin::test_login[su-wrong_password-failure-invalid_password]
[32mINFO    [0m selenium_test:logger.py:95 关闭浏览器
[32mINFO    [0m selenium_test:logger.py:95 ====== 测试结束: test_login ======
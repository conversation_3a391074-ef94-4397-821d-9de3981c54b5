import time
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.select import Select
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.action_chains import ActionChains
from utils.logger import Logger


class BasePage:
    """基础页面类，包含所有页面通用的方法"""

    def __init__(self, driver):
        """初始化基础页面类

        Args:
            driver: WebDriver实例
        """
        self.driver = driver
        self.timeout = 10  # 默认等待超时时间（秒）
        self.logger = Logger().get_logger()

    def find_element(self, locator, timeout=None):
        """查找元素并等待其可见

        Args:
            locator: 元素定位器，格式为(定位方式, 定位值)的元组
            timeout: 等待超时时间（秒），默认使用self.timeout

        Returns:
            找到的WebElement元素

        Raises:
            TimeoutException: 如果在指定时间内未找到元素
        """
        if timeout is None:
            timeout = self.timeout

        self.logger.info(f"查找元素: {locator}")
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.visibility_of_element_located(locator)
            )
            self.logger.info(f"元素已找到: {locator}")
            return element
        except TimeoutException:
            self.logger.error(f"元素未找到: {locator}")
            raise

    def click_element(self, locator, timeout=None):
        """查找元素并点击

        Args:
            locator: 元素定位器，格式为(定位方式, 定位值)的元组
            timeout: 等待超时时间（秒），默认使用self.timeout

        Returns:
            None
        """
        self.logger.info(f"点击元素: {locator}")
        element = self.find_element(locator, timeout)
        element.click()
        self.logger.info(f"元素已点击: {locator}")

    def input_text(self, locator, text, timeout=None):
        """查找元素并输入文本

        Args:
            locator: 元素定位器，格式为(定位方式, 定位值)的元组
            text: 要输入的文本
            timeout: 等待超时时间（秒），默认使用self.timeout

        Returns:
            None
        """
        self.logger.info(f"在元素 {locator} 中输入文本: {text}")
        element = self.find_element(locator, timeout)
        element.clear()  # 清除现有文本
        element.send_keys(text)
        self.logger.info(f"文本输入完成: {locator}")

    def get_text(self, locator, timeout=None):
        """获取元素的文本内容

        Args:
            locator: 元素定位器，格式为(定位方式, 定位值)的元组
            timeout: 等待超时时间（秒），默认使用self.timeout

        Returns:
            元素的文本内容
        """
        self.logger.info(f"获取元素文本: {locator}")
        element = self.find_element(locator, timeout)
        text = element.text
        self.logger.info(f"元素文本内容: {text}")
        return text

    def is_element_present(self, locator, timeout=5):
        """检查元素是否存在

        Args:
            locator: 元素定位器，格式为(定位方式, 定位值)的元组
            timeout: 等待超时时间（秒），默认为5秒

        Returns:
            布尔值，表示元素是否存在
        """
        self.logger.info(f"检查元素是否存在: {locator}")
        try:
            self.find_element(locator, timeout)
            self.logger.info(f"元素存在: {locator}")
            return True
        except (TimeoutException, NoSuchElementException):
            self.logger.info(f"元素不存在: {locator}")
            return False

    def wait_for_element_to_disappear(self, locator, timeout=None):
        """等待元素消失

        Args:
            locator: 元素定位器，格式为(定位方式, 定位值)的元组
            timeout: 等待超时时间（秒），默认使用self.timeout

        Returns:
            布尔值，表示元素是否消失
        """
        if timeout is None:
            timeout = self.timeout

        self.logger.info(f"等待元素消失: {locator}")
        try:
            WebDriverWait(self.driver, timeout).until_not(
                EC.visibility_of_element_located(locator)
            )
            self.logger.info(f"元素已消失: {locator}")
            return True
        except TimeoutException:
            self.logger.error(f"元素未消失: {locator}")
            return False

    def select_dropdown_by_text(self, locator, text, timeout=None):
        """通过可见文本选择下拉框选项

        Args:
            locator: 元素定位器，格式为(定位方式, 定位值)的元组
            text: 要选择的选项文本
            timeout: 等待超时时间（秒），默认使用self.timeout
        """
        self.logger.info(f"选择下拉框 {locator} 中的文本: {text}")
        element = self.find_element(locator, timeout)
        select = Select(element)
        select.select_by_visible_text(text)
        self.logger.info(f"下拉框选择完成: {text}")

    def select_dropdown_by_value(self, locator, value, timeout=None):
        """通过值选择下拉框选项

        Args:
            locator: 元素定位器，格式为(定位方式, 定位值)的元组
            value: 要选择的选项值
            timeout: 等待超时时间（秒），默认使用self.timeout
        """
        self.logger.info(f"选择下拉框 {locator} 中的值: {value}")
        element = self.find_element(locator, timeout)
        select = Select(element)
        select.select_by_value(value)
        self.logger.info(f"下拉框选择完成: {value}")

    def hover_element(self, locator, timeout=None):
        """将鼠标悬停在元素上

        Args:
            locator: 元素定位器，格式为(定位方式, 定位值)的元组
            timeout: 等待超时时间（秒），默认使用self.timeout
        """
        self.logger.info(f"将鼠标悬停在元素上: {locator}")
        element = self.find_element(locator, timeout)
        ActionChains(self.driver).move_to_element(element).perform()
        self.logger.info(f"鼠标悬停完成: {locator}")

    def scroll_to_element(self, locator, timeout=None):
        """滚动到元素位置

        Args:
            locator: 元素定位器，格式为(定位方式, 定位值)的元组
            timeout: 等待超时时间（秒），默认使用self.timeout
        """
        self.logger.info(f"滚动到元素位置: {locator}")
        element = self.find_element(locator, timeout)
        self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
        self.logger.info(f"滚动完成: {locator}")

    def get_page_title(self):
        """获取页面标题

        Returns:
            页面标题文本
        """
        title = self.driver.title
        self.logger.info(f"获取页面标题: {title}")
        return title

    def get_page_url(self):
        """获取当前页面URL

        Returns:
            当前页面URL
        """
        url = self.driver.current_url
        self.logger.info(f"获取当前页面URL: {url}")
        return url

    def refresh_page(self):
        """刷新当前页面"""
        self.logger.info("刷新当前页面")
        self.driver.refresh()

    def switch_to_frame(self, locator=None, frame_reference=None, timeout=None):
        """切换到iframe

        Args:
            locator: 元素定位器，格式为(定位方式, 定位值)的元组
            frame_reference: iframe的引用（索引、名称或WebElement）
            timeout: 等待超时时间（秒），默认使用self.timeout
        """
        if timeout is None:
            timeout = self.timeout

        if locator:
            self.logger.info(f"切换到iframe: {locator}")
            frame_element = self.find_element(locator, timeout)
            self.driver.switch_to.frame(frame_element)
        elif frame_reference is not None:
            self.logger.info(f"切换到iframe: {frame_reference}")
            WebDriverWait(self.driver, timeout).until(
                EC.frame_to_be_available_and_switch_to_it(frame_reference)
            )
        self.logger.info("切换iframe成功")

    def switch_to_default_content(self):
        """切换回主文档"""
        self.logger.info("切换回主文档")
        self.driver.switch_to.default_content()

    def accept_alert(self, timeout=None):
        """接受警告框

        Args:
            timeout: 等待超时时间（秒），默认使用self.timeout
        """
        if timeout is None:
            timeout = self.timeout

        self.logger.info("接受警告框")
        WebDriverWait(self.driver, timeout).until(EC.alert_is_present())
        alert = self.driver.switch_to.alert
        alert.accept()
        self.logger.info("警告框已接受")

    def dismiss_alert(self, timeout=None):
        """取消警告框

        Args:
            timeout: 等待超时时间（秒），默认使用self.timeout
        """
        if timeout is None:
            timeout = self.timeout

        self.logger.info("取消警告框")
        WebDriverWait(self.driver, timeout).until(EC.alert_is_present())
        alert = self.driver.switch_to.alert
        alert.dismiss()
        self.logger.info("警告框已取消")

    def take_screenshot(self, filename=None):
        """捕获屏幕截图

        Args:
            filename: 截图文件名，如果为None，则使用当前时间戳

        Returns:
            截图文件路径
        """
        if filename is None:
            filename = f"screenshot_{time.strftime('%Y%m%d_%H%M%S')}.png"

        self.logger.info(f"捕获屏幕截图: {filename}")
        screenshot_path = self.driver.save_screenshot(filename)
        self.logger.info(f"截图已保存: {filename}")
        return screenshot_path

    def execute_script(self, script, *args):
        """执行JavaScript代码

        Args:
            script: 要执行的JavaScript代码
            *args: 传递给JavaScript的参数

        Returns:
            JavaScript执行的结果
        """
        self.logger.info(f"执行JavaScript: {script}")
        return self.driver.execute_script(script, *args)

    def wait_for_page_load(self, timeout=None):
        """等待页面加载完成

        Args:
            timeout: 等待超时时间（秒），默认使用self.timeout
        """
        if timeout is None:
            timeout = self.timeout

        self.logger.info("等待页面加载完成")
        WebDriverWait(self.driver, timeout).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        self.logger.info("页面加载完成")
